<script setup lang="ts">
import { useAuthStore } from '~/store/useAuthStore.client'

const route = useRoute()
const authStore = useAuthStore()
const isLoggedIn = ref(false)

interface LinkChildren {
	title: string
	path?: string
}

interface LinkGroup {
	title: string
	children?: LinkChildren[] | undefined
}

interface Link {
	group: LinkGroup
	id: number
	isExpanded?: boolean
	show?: boolean | object
}

const currentRoute = computed(() =>
	route.path?.replace(/^\/(ar|en)/, ''),
)
const links = ref<Link[]>([
	{
		group: {
			title: 'footer.get-know',
			children: [
				{
					title: 'footer.us',
					path: '/about-us',
				},
				{
					title: 'footer.faq',
					path: '/faq',
				}, {
					title: 'footer.services',
					path: '/services',
				}, {
					title: 'footer.contact',
					path: '/contact-us',
				}, {
					title: 'footer.store',
					path: '/stores',
				}, {
					title: 'footer.complaints',
					path: '/complaints',
				},
			],
		},
		id: 1,
		isExpanded: false,
		show: true,
	},
	{
		group: {
			title: 'footer.experience',
			children: [
				{
					title: 'footer.why-action',
					path: '/why-action',
				},
				{
					title: 'footer.how-to-buy',
					path: '/how-to-buy',
				},
				{
					title: 'footer.brands',
					path: '/brands-list',
				},
			],
		},
		id: 2,
		isExpanded: false,
		show: true,
	},
	{
		group: {
			title: 'footer.important-links',
			children: [
				{
					title: 'footer.my-account',
					path: isLoggedIn.value ? '/my/profile' : `${currentRoute.value}?auth=login`,
				}, {
					title: 'footer.wish-list',
					path: `${currentRoute.value}?drawer=wishlist`,
				}, {
					title: 'footer.my-requests',
					path: `${currentRoute.value}?drawer=cart`,
				},
				{
					title: 'footer.blogs',
					path: 'https://action.jo/blog',
				},
			],
		},
		id: 3,
		isExpanded: false,
		show: true,
	},
	{
		group: {
			title: 'footer.customer-support',
		},
		id: 4,
		isExpanded: false,
		show: true,
	},
])

const onExpandList = (id: number, show = true) => {
	links.value = links.value.map(item => ({
		...item,
		isExpanded: item.id === id && show,
	}))
}

onMounted(() => {
	nextTick(() => {
		isLoggedIn.value = !!authStore.isLoggedIn
	})
})
</script>

<template>
	<div
		class="w-full md:flex-col xs:flex-row flex items-center justify-center bg-white border-t border-gray-200 shadow-lg mt-12 max-md:px-4"
	>
		<div class="grid container xs:gap-2 sm:gap-4 md:gap-6 py-8 xs:grid-cols-1 sm:grid-cols-4 md:grid-cols-6">
			<div class="flex flex-col gap-2 xs:col-span-1 sm:col-span-4 md:col-span-2">
				<div class="flex flex-col gap-4">
					<NuxtImg
						class="w-32"
						:alt="$t('app.action-mobile-logo')"
						:title="$t('app.action-mobile-logo')"
						src="/images/logo.png"
						width="128"
						height="67"
						format="webp"
						loading="lazy"
					/>
					<p class="text-sm text-gray-600 font-normal leading-loose">
						{{
							$t('home.about-text')
						}}
					</p>

					<div class="flex w-full flex-col gap-4 max-md:flex-row max-md:mb-4 max-md:justify-center">
						<div class="flex gap-4 items-center">
							<NuxtLink
								:external="true"
								target="_blank"
								to="https://x.com/actionwebsite"
								class="bg-primary-600 rounded-full h-9 w-9 flex flex-col justify-center items-center"
							>
								<Icon
									name="ui:x"
									class="text-white"
									view-box="0 0 20 21"
									size="20px"
									loading="lazy"
								/>
							</NuxtLink>
							<NuxtLink
								:external="true"
								target="_blank"
								to="https://www.snapchat.com/add/actionwebsite"
								class="bg-primary-600 rounded-full h-9 w-9 flex flex-col justify-center items-center"
							>
								<Icon
									name="ui:snapchat"
									class="text-white"
									size="20px"
									loading="lazy"
								/>
							</NuxtLink>
							<NuxtLink
								:external="true"
								target="_blank"
								to="https://www.instagram.com/actionwebsite/"
								class="bg-primary-600 rounded-full h-9 w-9 flex flex-col justify-center items-center"
							>
								<Icon
									name="ui:instagram"
									class="text-white"
									size="20px"
									loading="lazy"
								/>
							</NuxtLink>
							<NuxtLink
								:is-external="true"
								target="_blank"
								to="https://www.facebook.com/Actionmobile11"
								class="bg-primary-600 rounded-full h-9 w-9 flex flex-col justify-center items-center"
							>
								<Icon
									name="ui:facebook"
									class="text-white"
									size="20px"
									loading="lazy"
								/>
							</NuxtLink>
						</div>

						<div
							v-if="false"
							class="flex gap-4 items-center "
						>
							<NuxtLink
								:external="true"
								to="/static"
								target="_blank"
							>
								<NuxtImg
									src="/images/google-play.png"
									alt="Google Play"
									width="110"
									height="35"
									loading="lazy"
								/>
							</NuxtLink>

							<NuxtLink
								:external="true"
								to="/static"
								target="_blank"
							>
								<NuxtImg
									src="/images/app-store.png"
									alt="Apple Store"
									width="110"
									height="35"
									loading="lazy"
								/>
							</NuxtLink>
						</div>
					</div>
				</div>
			</div>
			<div
				v-for="(link, index) in [...links].splice(0, 3)"
				:key="`link-${index}`"
				:class="{ hidden: !link.show }"
				class="col-span-1 flex flex-col sm:gap-8 px-4 max-md:px-0 max-sm:border-b max-sm:border-gray-300 max-sm:pb-4"
			>
				<div
					class="flex justify-between items-center max-sm:py-2 transition-transform"
					@click="onExpandList(link.id, !link.isExpanded)"
				>
					<span class="font-bold text-gray-800 sm:text-lg">{{ $t(link.group.title) }}</span>
					<button>
						<Icon
							name="lucide:chevron-down"
							class="sm:hidden"
							:class="{ 'rotate-180': link.isExpanded }"
						/>
					</button>
				</div>
				<nav
					class="flex flex-col gap-4 transition-transform"
					:class="{ 'max-sm:h-0 max-sm:overflow-hidden': !link.isExpanded }"
				>
					<NuxtLinkLocale
						v-for="(subLink, subIndex) in link.group?.children"
						:key="`link-${index}-${subIndex}`"
						:to="subLink?.path"
						class="cursor-pointer"
					>
						<span class="text-sm text-normal text-gray-500 text-nowrap">{{ $t(subLink.title) }}</span>
					</NuxtLinkLocale>
				</nav>
			</div>

			<div
				:key="`key-${links?.[3]?.isExpanded}`"
				class="col-span-1 flex flex-col sm:gap-8 px-4 max-md:px-0 max-sm:pb-8"
			>
				<div
					class="flex justify-between items-center max-sm:py-2 transition-transform"
					@click.prevent="onExpandList(4, !links[3]?.isExpanded)"
				>
					<span class="font-bold text-gray-800 sm:text-xl">{{ $t('footer.customer-support') }}</span>
					<button>
						<Icon
							name="lucide:chevron-down"
							class="sm:hidden"
							:class="{ 'rotate-180': links[3]?.isExpanded }"
						/>
					</button>
				</div>
				<nav
					class="flex flex-col gap-4 transition-transform text-start"
					:class="{ 'max-sm:h-0 max-sm:overflow-hidden': !links[3]?.isExpanded }"
				>
					<span class="text-sm text-normal text-gray-500">
						{{ $t('footer.our-support-team') }}
					</span>

					<a
						href="tel:+962-791009595"
						class="text-base text-gray-500"
					>
						<span dir="ltr">
							+962-791009595
						</span>
					</a>

					<a
						href="mailto:<EMAIL>"
						class="text-base text-gray-500"
					>
						<EMAIL>
					</a>

					<a
						href="https://wa.me/962791009595"
						target="_blank"
						rel="noopener noreferrer"
						class="text-base text-gray-500"
					>
						<NuxtImg
							src="/images/whatsapp.png"
							width="35px"
							height="35px"
							loading="lazy"
							alt="whatsapp"
							title="whatsapp"
							format="webp"
						/>
					</a>
				</nav>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">

</style>
