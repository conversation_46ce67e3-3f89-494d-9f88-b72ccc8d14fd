// import type { Fetch } from 'ofetch'
import { toast } from 'vue-sonner'
import { useUserToken } from '~/composables/useUserToken'
import { useAuthStore } from '~/store/useAuthStore.client'

interface ApiResponse {
	status?: number
	message?: string | string[]
	_data?: { message?: string | string[] }
}

export default defineNuxtPlugin({
	name: 'api',
	setup(nuxtApp) {
		const config = useRuntimeConfig()
		const localePath = useLocalePath()
		const { visitorId } = useVisitorId()
		const $i18n = nuxtApp.$i18n
		const $api = $fetch.create({
			baseURL: config.public.baseUrl as string,
			timeout: 20000,
			mode: 'cors',
			onRequest({ options, request }) {
				const { userToken } = useUserToken()

				if ($i18n['locale']) {
					options.headers.set('Language', $i18n['locale']?.value)
				}

				if (visitorId.value) {
					options.headers.set('visitor-id', visitorId.value)
				}

				if (userToken.value) {
					options.headers.set('authorization', `Bearer ${userToken.value}`)
				} else if (request.toString().includes('/my/')) {
					throw new Error('Unauthorized request blocked: /my/ route requires token.')
				}
			},
			async onResponseError({ response, request }) {
				const { status, message, _data } = response as ApiResponse
				const authStore = useAuthStore()
				switch (status) {
					case 401:
						if (authStore.isLoggedIn) {
							authStore.removeUserSession()
							// toast.error($i18n['t']('error.unauthorized'))
						}
						navigateTo(localePath({ path: '/' }))
						break
					case 406:
						toast.error('Something went wrong, 406')
						navigateTo(localePath({ name: 'home' }))
						break
					case 403:
						toast.error('Something went wrong, 403')
						throw createError({
							statusCode: 403,
						})
					default:
						console.log('API Error:', request.toString(), status, _data?.message || message)
						toast.error(_data?.message || message || 'Something went wrong')
				}
			},
		})

		return {
			provide: {
				api: $api,
			},
		}
	},
})
