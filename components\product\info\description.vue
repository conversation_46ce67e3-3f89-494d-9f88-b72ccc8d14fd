<script setup lang="ts">
import type { Details, ProductAttribute, UseProductDetails } from '~/interfaces/product/details'

const { product, loading = true } = defineProps<{
	product: UseProductDetails | Details | undefined
	loading?: boolean
}>()

const description = computed<string>(() => product?.description as string)
const { localeProperties } = useI18n()

const allAttributes = computed(() => [...(product?.variance?.attributes || []), ...(product?.attributes || [])] as ProductAttribute[])
</script>

<template>
	<div
		class="flex w-full justify-between py-6 relative md:flex-row xs:flex-col-reverse sm:overflow-hidden xs:!overflow-y-auto gap-y-6"
	>
		<div class="flex flex-col md:w-1/2 gap-4 px-4">
			<template v-if="loading">
				<Skeleton class="h-6 w-40" />
			</template>
			<template v-else>
				<h3 class="text-md font-semibold">
					{{ $t('product.description-title') }}
				</h3>
			</template>
			<template v-if="loading">
				<Skeleton
					v-for="(_, index) in Array(4)"
					:key="`select-loader-${index}`"
					class="h-4 w-full"
				/>
			</template>
			<template v-else>
				<div
					class="description flex flex-col pb-10 overflow-hidden"
					v-html="description"
				/>
			</template>
		</div>
		<div class="flex flex-col md:w-1/2 gap-4 px-4">
			<template v-if="loading">
				<Skeleton class="h-6 w-40" />
			</template>
			<template v-else-if="allAttributes.length">
				<h3 class="text-md font-semibold">
					{{ $t('product.speciation-title') }}
				</h3>
				<div class="flex flex-wrap w-full gap-4">
					<template v-if="loading">
						<Skeleton
							v-for="(_, index) in Array(4)"
							:key="`select-loader-${index}`"
							class="h-10 w-20 me-2"
						/>
					</template>
					<template v-else>
						<div
							v-for="variant in allAttributes"
							:key="`selected-${variant.attributeId}`"
							class="flex items-center bg-sky-50 py-2 px-4 rounded-lg flex-col min-w-32"
						>
							<div class="flex gap-2 items-center">
								<Icon
									:name="`ui:${variant.icon || 'colors'}`"
									class="text-primary-600"
								/>

								<span class="text-sm font-semibold text-primary-500">{{ variant.name }}</span>
							</div>

							<div class="flex text-sm">
								{{ variant.value?.number ?? variant.value?.value ?? variant.value?.name }} {{ variant?.suffix }}
							</div>
						</div>
					</template>
				</div>
			</template>
		</div>

		<!--		<button -->
		<!--			v-if="!loading" -->
		<!--			class="absolute bottom-0 flex w-full bg-gray-100 bg-opacity-50 py-5 items-center justify-center drop-shadow-2xl" -->
		<!--			@click.prevent="() => isViewAll = !isViewAll" -->
		<!--		> -->
		<!--			<div class="font-medium text-sm underline"> -->
		<!--				<span v-if="!isViewAll"> -->
		<!--					{{ $t('product.show-full-description') }} -->
		<!--				</span> -->
		<!--				<span v-else> -->
		<!--					{{ $t('product.hide-full-description') }} -->
		<!--				</span> -->
		<!--			</div> -->
		<!--		</button> -->
	</div>
</template>
