<script setup lang="ts">
import { Card, CardContent } from '@/components/ui/card'
import {
	Carousel,
	CarouselContent,
	CarouselItem,
} from '@/components/ui/carousel'

interface Source {
	lg?: string
	md?: string
	sm?: string
}
interface Item {
	source: Source
}
const list: Item[] = [
	{
		source: {
			lg: '/images/hero/lg.png',
			md: '/images/hero/md.png',
			sm: '/images/hero/sm.png',
		},
	},
]

const isMounted = useMounted()
</script>

<template>
	<div class="w-full sm:w-auto col-span-3">
		<Carousel
			class="relative w-full"
			:opts="{
				active: isMounted,
			}"
		>
			<CarouselContent>
				<CarouselItem
					v-for="(slide, index) in list"
					:id="`slide-${index}`"
					:key="`slide-${index}`"
					class="!p-0"
					disabled
				>
					<Card class="p-0">
						<CardContent class="relative flex aspect-video w-full !p-0 sm:h-96 xs:h-44">
							<div class="flex absolute w-full h-full z-1">
								<div class="flex flex-col gap-4 xs:p-4 sm:ps-20 sm:pt-12">
									<h2 class="text-white font-bold max-w-96 leading-normal text-md sm:text-4xl">
										{{ $t('home.hero-title') }}
									</h2>
									<p class="text-white font-semibold w-full md:w-1/2 sm:w-2/3 leading-normal text-xs sm:text-2xl">
										{{ $t('home.hero-sub-title') }}
									</p>
									<Button
										as="a"
										href=""
										variant="white"
										class="max-w-28 h-6 px-2 sm:h-10 sm:max-w-44"
									>
										<span class="text-xs sm:text-lg font-semibold">{{ $t('home.widget-pay-now') }}</span>
									</Button>
								</div>
							</div>
							<NuxtImg
								:src="slide.source.lg"
								:alt="$t('app.marketing-alt')"
								:title="$t('app.marketing-alt')"
								quality="90"
								:preload="true"
								height="100%"
								width="1772"
								loading="eager"
								format="webp"
								class="object-cover object-start w-full h-full hidden md:flex"
							/>
							<!-- <NuxtImg
								:src="slide.source.md"
								:alt="$t('app.marketing-alt')"
								:title="$t('app.marketing-alt')"
								quality="90"
								:preload="true"
								height="100%"
								width="992"
								loading="eager"
								format="webp"
								class="object-cover object-start w-full h-full aspect-auto hidden sm:flex"
							/>
							<NuxtImg
								:src="slide.source.sm"
								:alt="$t('app.marketing-alt')"
								:title="$t('app.marketing-alt')"
								quality="90"
								:preload="true"
								height="100%"
								width="398"
								loading="eager"
								format="webp"
								class="object-cover object-start w-full h-full aspect-auto hidden xs:flex"
							/> -->
						</CardContent>
					</Card>
				</CarouselItem>
			</CarouselContent>
		</Carousel>
	</div>
</template>
