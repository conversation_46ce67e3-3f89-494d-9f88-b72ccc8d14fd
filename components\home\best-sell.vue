<script setup lang="ts">
import { CarouselItem } from '~/components/ui/carousel'
import type { Item as Product } from '~/interfaces/product/product-list'

const limit = 10
const { data, error, status } = await useApi<Product[]>('most-popular', {
	query: { limit },
})

if (error.value) {
	console.error('Error fetching best sell:', error.value)
}

const products = computed<Product[]>(() => data.value as Product[])
const loading = computed<boolean>(() => status.value !== 'success')
</script>

<template>
	<Card class="col-span-3 flex flex-col max-md:col-span-3">
		<div class="px-4 py-4 flex justify-between items-center">
			<span class="text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl">
				{{ $t('home.best-sell-title') }}
			</span>
			<NuxtLinkLocale
				:to="`/category/smartphones`"
				class="text-primary-600 hidden max-sm:flex"
			>
				{{ $t('home.see-more') }}
			</NuxtLinkLocale>
		</div>
		<CardContent class="grid md:grid-cols-4 xs:grid-cols-2 gap-4 px-4 place-items-center">
			<Carousel
				:opts="{ align: 'start', slidesToScroll: 'auto' }"
				class="w-full col-span-4"
			>
				<template #default="{ canScrollNext, canScrollPrev }">
					<CarouselContent class="gap-x-4">
						<template v-if="loading">
							<CarouselItem
								v-for="(_, index) in Array(4)"
								:key="`product-skeleton-${index}`"
								class="flex flex-col rounded h-full border p-2 border-gray-200 max-w-1/4 max-w-72 me-6 w-full"
							>
								<Skeleton class="w-full h-32 bg-gray-200 mb-2" />
								<Skeleton class="w-4/5 h-5 bg-gray-200 mb-2" />
								<Skeleton class="w-1/3 h-5 bg-gray-200 mb-2" />
							</CarouselItem>
						</template>
						<template v-else>
							<CarouselItem
								v-for="product in products"
								:key="product.productId"
								class="max-sm:max-w-52 max-sm:w-full sm:basis-4/10 max-w-72 !p-0"
							>
								<ProductCard
									:key="`product-${product.productId}`"
									:product="product"
								/>
							</CarouselItem>
						</template>
					</CarouselContent>
					<CarouselPrevious v-if="!loading && canScrollPrev" />
					<CarouselNext v-if="!loading && canScrollNext" />
				</template>
			</Carousel>
		</CardContent>
	</Card>
</template>
