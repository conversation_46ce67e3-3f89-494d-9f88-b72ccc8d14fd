<script setup lang="ts">
import { toast } from 'vue-sonner'

const { shippingCarrierId } = defineProps<{
	shippingCarrierId?: number
}>()
const emit = defineEmits<{
	(event: 'close:modal'): void
}>()

interface Review {
	rating?: number
	text: string
	value: number
}

const form = ref({
	rating: 0,
	review: '',
})
const route = useRoute()
const { data, error } = await useApi<Review[]>('/lookups-website/complaints')

if (error.value) {
	console.error('API Error: fetching lockup rating', error.value)
}

const saving = ref<boolean>(false)
const reviews = computed<Review[]>(() => data.value as Review[] || [])
const disableSubmit = computed(() => reviews.value.find(r => !r.rating) || !reviews.value.length)

const { t } = useI18n()
const submitForm = async () => {
	saving.value = true
	const { $api } = useNuxtApp()
	const dataBody = reviews.value.map((review: Review) => {
		return {
			complaintId: review.value,
			rating: review.rating,
			review: form.value.review,
			model_type: 'order',
			model_id: Number(route.params.orderId),
		}
	})

	return $api<unknown>(`/complaints/rating`, {
		method: 'POST',
		body: dataBody,
	})
		.then(() => {
			toast.success(t('form.submit-rate-success'))
			emit('close:modal')
		})
		.catch((error) => {
			console.log(`Error on rating order ${error}`)
		})
		.finally(() => {
			nextTick(() => saving.value = false)
		})
}
</script>

<template>
	<Modal
		:title="$t('orders.review-delivered-experience-btn')"
		@close="emit('close:modal')"
	>
		<template #body>
			<div class="flex w-full flex-col px-4 gap-4 max-h-[60vh] h-full overflow-y-auto">
				<div class="flex w-full flex-col h-53 py-4 bg-sky-50 justify-center items-center gap-4 rounded-lg">
					<div class="flex gap-2 flex-col justify-center items-center">
						<Icon
							name="ui:delivery-review"
							size="90px"
						/>
					</div>
					<div class="flex justify-center items-center">
						<span class="text-base ">{{ $t('orders.delivery-review-text') }}</span>
					</div>
				</div>
				<div class="flex flex-col gap-2">
					<div
						v-for="review in reviews"
						:key="review.value"
						class="flex flex-col gap-2 w-full"
					>
						<span class="text-sm">{{ review.text }}</span>
						<div class="flex w-full gap-2">
							<button
								v-for="(_, index) in Array(5)"
								:key="`form-rating${review.value}-${index}`"
								:class="review.rating >= 1 + index ? 'text-rating-200' : 'text-rating-100'"
								@click.prevent="() =>	review.rating = 1 + index"
							>
								<Icon
									name="ui:rate-star"
									size="18px"
								/>
							</button>
						</div>
					</div>

					<textarea
						v-model="form.review"
						name="description"
						:placeholder="$t('review.feedback-placeholder')"
						class="border border-gray-300 rounded-md text-sm font-medium text-gray-500 p-3 outline-none resize-none"
						rows="5"
					/>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="flex justify-end w-full gap-4 mt-4 border-t border-gray-200 pt-4">
				<Button
					variant="outline"
					@click="emit('close:modal')"
				>
					{{ $t('form.cancel') }}
				</Button>

				<Button
					:disabled="disableSubmit"
					:loading="saving"
					@click.prevent="() => submitForm()"
				>
					{{ $t('form.submit-rate-title') }}
				</Button>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">

</style>
