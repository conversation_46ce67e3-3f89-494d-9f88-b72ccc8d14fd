<script setup lang="ts">
import RateForm from '~/components/product/info/rate-form.vue'
import RateRow from '~/components/product/info/rate-row.vue'
import UserRate from '~/components/product/info/user-rate.vue'
import type { Details } from '~/interfaces/product/details'
import type { Rate, Rating, Review } from '~/interfaces/product/rate'

const { product } = defineProps<{
	product: Details | undefined
}>()

const { data, error, status } = await useApi<Rate>(`ratings/${product.productId}`)

if (error.value) {
	console.error('Error on fetching product rating', error.value)
}
const loading = computed<boolean>(() => status.value === 'pending')
const rate = computed<Rate>(() => data.value as Rate)

const reviews = computed<Review[]>(() => {
	/** prepare the review array **/
	const ratings = (rate.value as Rate)?.ratings as Rating[]
	return ratings?.flatMap(rating => rating?.reviews?.map(review => ({
		...review,
		rating: rating?.rating || 1,
	}))) as Review[]
})
</script>

<template>
	<div class="flex w-full justify-between py-6 relative md:flex-row xs:flex-col">
		<div class="flex md:w-1/2 gap-4 flex-col border-e border-gray-200 p-4">
			<template v-if="loading">
				<Skeleton class="h-6 w-32" />
			</template>
			<template v-else>
				<div class="flex text-gray-500 font-semibold text-md">
					{{ $t('product.general-rate') }}
				</div>
			</template>
			<div class="flex gap-2 items-center">
				<template v-if="loading">
					<div class="flex w-full flex-col">
						<Skeleton class="h-4 w-32" />
						<Skeleton class="h-5 w-44 mt-2" />
					</div>
				</template>
				<template v-else>
					<div class="flex gap-1">
						<Icon
							v-for="star in 5"
							:key="star"
							name="ui:rate-star"
							:class="star <= rate?.avgRating ? 'text-green-500' : 'text-gray-200'"
							class="w-5 h-5 drop-shadow-sm"
						/>
					</div>

					<div class="text-lg font-bold">
						{{ `${rate?.avgRating} ${$t('text.of')} 5` }}
					</div>
				</template>
			</div>

			<template v-if="loading">
				<Skeleton class="h-5 w-32" />
			</template>
			<template v-else>
				<div class="flex text-gray-500 text-md ">
					{{ $t('product.review-details', { reviews: rate?.avgRating }) }}
				</div>
			</template>

			<template v-if="loading">
				<Skeleton
					v-for="(_, index) in Array(5)"
					:key="`rate-priority-loading-${index}`"
					class="h-5 w-full"
				/>
			</template>
			<template v-else>
				<div class="flex flex-col w-full gap-1">
					<rate-row
						v-for="rt in rate?.ratings"
						:key="`rate-priority-${rt.rating}`"
						:rate="rt"
					/>
				</div>
			</template>

			<div
				v-if="!loading"
				class="flex py-4"
			>
				<rate-form :product-id="product?.productId" />
			</div>
		</div>

		<div class="flex md:w-1/2 flex-col gap-4 px-6">
			<template v-if="loading">
				<Skeleton class="h-6 w-32" />
				<div class="flex w-full gap-2">
					<Skeleton class="h-9 w-9 rounded-full me-2" />

					<div class="flex flex-col flex-grow w-full gap-2">
						<Skeleton class="h-3 w-32" />
						<Skeleton class="h-3 max-w-full" />
						<Skeleton class="h-3 max-w-full" />
					</div>
				</div>
			</template>
			<template v-else>
				<div class="flex text-gray-700 text-base border-b border-gray-200 pb-2">
					{{ $t('product.review-details', { reviews: rate?.avgRating }) }}
				</div>
				<template v-if="reviews?.length">
					<user-rate
						v-for="review in reviews"
						:key="`user-rate-${review.ratingId}`"
						:review="review"
					/>
				</template>
				<template v-else>
					<div class="flex w-full flex-col gap-4 justify-center items-center">
						<Icon
							name="ui:rating-zero"
							size="290px"
						/>
						<span>{{ $t('product.zero-rating-title') }}</span>
					</div>
				</template>
			</template>
		</div>
	</div>
</template>
