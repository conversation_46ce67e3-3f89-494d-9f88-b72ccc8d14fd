<script setup lang="ts">
import type { NewArrival } from '~/interfaces/product/new-arrival'

const { productId } = defineProps<{
	productId?: number
}>()
const { data, error, status } = await useApi<NewArrival[]>(`/suggested-product/${productId}`, {
	query: { limit: 8 },
})

if (error.value) {
	console.error('Error fetching new arrival:', error.value)
}

const products = computed(() => data.value as NewArrival[])
const loading = computed(() => status.value !== 'success')
</script>

<template>
	<horizantal-cards
		v-if="products?.length && !loading"
		class="my-8"
		:title="$t('product.might-like')"
		:products="products"
		:loading="loading"
	/>
	<div
		v-else
		class="my-8"
	/>
</template>
